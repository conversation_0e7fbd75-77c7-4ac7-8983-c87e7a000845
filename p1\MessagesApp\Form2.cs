namespace MessagesApp;

/// <summary>
/// Form2 - نافذة غرفة الرسائل
/// هذه النافذة تمثل غرفة الرسائل التي يتم فتحها من النافذة الرئيسية
/// </summary>
public partial class Form2 : Form
{
    /// <summary>
    /// منشئ Form2
    /// يقوم بتهيئة المكونات والإعدادات الأساسية للنافذة
    /// </summary>
    public Form2()
    {
        InitializeComponent();
    }

    /// <summary>
    /// حدث تحميل النافذة
    /// يتم استدعاؤه عند فتح النافذة لأول مرة
    /// </summary>
    private void Form2_Load(object sender, EventArgs e)
    {
        // يمكن إضافة أي كود تهيئة إضافي هنا
        // مثل تحميل الرسائل من قاعدة البيانات
    }

    /// <summary>
    /// حدث الضغط على زر الإغلاق (اختياري)
    /// يمكن إضافة زر إغلاق مخصص إذا أردنا
    /// </summary>
    private void btnClose_Click(object sender, EventArgs e)
    {
        // إغلاق النافذة الحالية
        this.Close();
    }
}
