===============================================
تمرين 1: افتح غرفة الرسائل - Navigating with Show()
===============================================

🎯 مرحباً بك في التمرين الأول لتعلم Windows Forms!

📁 محتويات المجلد:
-----------------
📄 شرح_المشروع_غرفة_الرسائل.txt    - شرح شامل للمشروع وأهدافه
📄 شرح_الكود_بالتفصيل.txt           - شرح مفصل لكل سطر في الكود
📄 المشاكل_والحلول.txt              - المشاكل التي واجهتني وكيف حليتها
📄 README.txt                        - هذا الملف (دليل سريع)
💻 MessagesApp.exe                   - الملف التنفيذي للمشروع
📁 MessagesApp/                      - مجلد المشروع الكامل

🚀 كيفية تشغيل المشروع:
------------------------

الطريقة الأولى (الملف التنفيذي):
1. انقر نقراً مزدوجاً على MessagesApp.exe
2. ستظهر النافذة الرئيسية "لوحة التحكم"
3. اضغط على زر "اذهب إلى الرسائل"
4. ستفتح نافذة "غرفة الرسائل"

الطريقة الثانية (من الكود المصدري):
1. افتح Command Prompt
2. انتقل إلى مجلد MessagesApp
3. اكتب: dotnet run
4. اتبع نفس الخطوات أعلاه

🎓 ما ستتعلمه:
---------------
✅ إنشاء مشروع Windows Forms
✅ التنقل بين النوافذ باستخدام Show()
✅ الفرق بين Show() و ShowDialog()
✅ تصميم واجهات المستخدم
✅ معالجة أحداث الأزرار
✅ تخصيص الألوان والخطوط

🔧 المتطلبات:
--------------
- نظام التشغيل Windows
- .NET 8.0 أو أحدث (للتطوير)
- لا توجد متطلبات إضافية لتشغيل الملف التنفيذي

📚 الملفات التعليمية:
--------------------

1️⃣ شرح_المشروع_غرفة_الرسائل.txt:
   - وصف شامل للمشروع
   - الأهداف التعليمية
   - هيكل المشروع
   - المفاهيم المستخدمة

2️⃣ شرح_الكود_بالتفصيل.txt:
   - شرح كل ملف في المشروع
   - تحليل الكود سطر بسطر
   - المفاهيم التقنية
   - نصائح للمبتدئين

3️⃣ المشاكل_والحلول.txt:
   - المشاكل التي واجهتها أثناء التطوير
   - الحلول المطبقة
   - نصائح لتجنب المشاكل
   - الدروس المستفادة

🎮 كيفية استخدام التطبيق:
-------------------------

1. عند تشغيل التطبيق، ستظهر النافذة الرئيسية بلون أزرق فاتح
2. اضغط على زر "اذهب إلى الرسائل"
3. ستفتح نافذة جديدة بلون أخضر فاتح (غرفة الرسائل)
4. لاحظ أن النافذة الأصلية ما زالت مفتوحة ونشطة
5. يمكنك التنقل بين النافذتين بحرية
6. اضغط "إغلاق" في نافذة الرسائل للعودة للنافذة الرئيسية
7. أو أغلق أي نافذة باستخدام زر X

🔍 نقاط مهمة للملاحظة:
-----------------------

✨ Show() vs ShowDialog():
- Show(): النافذة الأصلية تبقى نشطة (Non-Modal)
- ShowDialog(): النافذة الأصلية تصبح غير نشطة (Modal)

✨ إدارة النوافذ:
- يمكن فتح عدة نوافذ في نفس الوقت
- كل نافذة لها دورة حياة منفصلة
- إغلاق النافذة الرئيسية يغلق التطبيق كاملاً

✨ التصميم:
- استخدام ألوان متناسقة
- خطوط واضحة تدعم العربية
- تنسيق مناسب للمكونات

💡 تطويرات مقترحة:
-------------------

للطلاب المتقدمين، يمكن إضافة:
🔹 المزيد من النوافذ (Form3, Form4)
🔹 قائمة تنقل (Menu Bar)
🔹 أيقونات مخصصة للنوافذ
🔹 حفظ موقع النوافذ
🔹 إضافة أصوات للأحداث
🔹 تطبيق نظام الألوان الداكنة/الفاتحة

🆘 المساعدة والدعم:
-------------------

إذا واجهت أي مشاكل:
1. راجع ملف "المشاكل_والحلول.txt"
2. تأكد من تثبيت .NET بشكل صحيح
3. تحقق من أن النظام يدعم Windows Forms
4. جرب تشغيل التطبيق كمدير (Run as Administrator)

📞 معلومات إضافية:
------------------

- المشروع مكتوب بلغة C# باستخدام .NET 8.0
- يستخدم Windows Forms للواجهة الرسومية
- الكود مُعلق باللغة العربية للوضوح
- جميع الملفات بترميز UTF-8 لدعم العربية

🎉 تهانينا!
-----------

بإكمال هذا التمرين، تكون قد تعلمت:
✅ أساسيات Windows Forms
✅ التنقل بين النوافذ
✅ معالجة الأحداث
✅ تصميم واجهات المستخدم

استعد للتمرين التالي! 🚀

===============================================
تم إنشاء هذا المشروع لأغراض تعليمية
نتمنى لك تعلماً ممتعاً ومفيداً! 📚
===============================================
