===============================================
تمرين 1: افتح غرفة الرسائل - Navigating with Show()
===============================================

📋 وصف المشروع:
--------------
هذا التمرين يهدف إلى تعليم الطلاب كيفية التنقل بين النوافذ (Forms) في تطبيقات Windows Forms
باستخدام الطريقة Show() بدلاً من ShowDialog().

🎯 أهداف التعلم:
-----------------
1. فهم الفرق بين Show() و ShowDialog()
2. تعلم كيفية إنشاء نوافذ متعددة في تطبيق واحد
3. إدارة التنقل بين النوافذ
4. التعامل مع الأحداث (Events) في Windows Forms
5. تخصيص مظهر النوافذ (الألوان والنصوص)

🔧 المتطلبات التقنية:
--------------------
- .NET Framework 4.7.2 أو أحدث
- Visual Studio 2019 أو أحدث
- Windows Forms Application
- نظام التشغيل Windows

📁 هيكل المشروع:
-----------------
MessagesApp/
├── Program.cs              (نقطة البداية للتطبيق)
├── Form1.cs               (النموذج الرئيسي - لوحة التحكم)
├── Form1.Designer.cs      (تصميم النموذج الرئيسي)
├── Form2.cs               (نموذج غرفة الرسائل)
├── Form2.Designer.cs      (تصميم نموذج غرفة الرسائل)
└── MessagesApp.csproj     (ملف إعدادات المشروع)

🎨 وصف واجهة المستخدم:
-----------------------

📱 Form1 (النموذج الرئيسي):
- العنوان: "لوحة التحكم الرئيسية"
- يحتوي على زر بعنوان "اذهب إلى الرسائل"
- لون الخلفية: أزرق فاتح
- حجم النافذة: 400x300

📱 Form2 (غرفة الرسائل):
- العنوان: "غرفة الرسائل"
- يحتوي على Label مكتوب فيه "مرحبًا بك في غرفة الرسائل"
- لون الخلفية: أخضر فاتح
- حجم النافذة: 500x350

⚙️ وظائف التطبيق:
------------------
1. عند تشغيل التطبيق، يظهر Form1 (لوحة التحكم)
2. عند الضغط على زر "اذهب إلى الرسائل"، يتم فتح Form2
3. Form1 تبقى مفتوحة ومرئية أثناء عرض Form2
4. يمكن للمستخدم التنقل بين النافذتين بحرية
5. يمكن إغلاق أي نافذة بشكل منفصل

🔍 المفاهيم المستخدمة:
----------------------
- Windows Forms Application
- Multiple Forms Management
- Event Handling (معالجة الأحداث)
- Show() vs ShowDialog()
- Form Properties (خصائص النماذج)
- Button Click Events
- Form Design and Layout

📚 الفرق بين Show() و ShowDialog():
-----------------------------------

🔹 Show():
- يفتح النافذة الجديدة بشكل غير مودال (Non-Modal)
- النافذة الأصلية تبقى نشطة ويمكن التفاعل معها
- يمكن فتح عدة نوافذ في نفس الوقت
- المستخدم يمكنه التنقل بين النوافذ بحرية

🔹 ShowDialog():
- يفتح النافذة الجديدة بشكل مودال (Modal)
- النافذة الأصلية تصبح غير نشطة
- يجب إغلاق النافذة الجديدة قبل العودة للأصلية
- يتم حجب التفاعل مع النوافذ الأخرى

🚀 خطوات تشغيل المشروع:
------------------------
1. افتح Visual Studio
2. افتح المشروع MessagesApp.sln
3. اضغط F5 أو "Start Debugging"
4. ستظهر النافذة الرئيسية (Form1)
5. اضغط على زر "اذهب إلى الرسائل"
6. ستفتح نافذة غرفة الرسائل (Form2)
7. لاحظ أن النافذة الأصلية ما زالت مفتوحة

🎓 ما ستتعلمه من هذا التمرين:
------------------------------
1. إنشاء مشروع Windows Forms جديد
2. إضافة نماذج جديدة للمشروع
3. تصميم واجهات المستخدم باستخدام Designer
4. ربط الأحداث بالأزرار
5. استخدام Show() لفتح النوافذ
6. تخصيص خصائص النماذج (الألوان، الأحجام، العناوين)
7. إدارة عدة نوافذ في تطبيق واحد

💡 نصائح مهمة:
---------------
- تأكد من إضافة using System.Windows.Forms في بداية الملفات
- استخدم أسماء واضحة للمتحكمات (Controls)
- اختبر التطبيق بفتح وإغلاق النوافذ عدة مرات
- لاحظ سلوك التطبيق عند إغلاق النافذة الرئيسية

🔧 تطويرات مقترحة:
-------------------
1. إضافة زر "إغلاق" في Form2 للعودة إلى Form1
2. إضافة المزيد من النوافذ (Form3, Form4)
3. إضافة قائمة (Menu) للتنقل بين النوافذ
4. حفظ موقع النوافذ عند الإغلاق
5. إضافة أيقونات مخصصة للنوافذ

===============================================
هذا التمرين يعتبر أساس مهم لفهم التنقل في تطبيقات Windows Forms
===============================================
