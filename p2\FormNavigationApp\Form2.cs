namespace FormNavigationApp;

/// <summary>
/// Form2 - غرفة التحكم الفرعية
/// هذه النافذة تمثل غرفة تحكم فرعية في النظام
/// </summary>
public partial class Form2 : Form
{
    /// <summary>
    /// منشئ Form2 - غرفة التحكم الفرعية
    /// يقوم بتهيئة المكونات والإعدادات الأساسية
    /// </summary>
    public Form2()
    {
        InitializeComponent();
    }

    /// <summary>
    /// حدث الضغط على زر "رجوع إلى غرفة القيادة"
    /// يقوم بإظهار النافذة الرئيسية وإغلاق النافذة الحالية
    /// </summary>
    /// <param name="sender">المكون الذي أثار الحدث</param>
    /// <param name="e">معلومات الحدث</param>
    private void btnReturnToMain_Click(object sender, EventArgs e)
    {
        try
        {
            // البحث عن Form1 في النوافذ المفتوحة
            // Application.OpenForms يحتوي على جميع النوافذ المفتوحة (المرئية والمخفية)
            Form1? mainForm = Application.OpenForms["Form1"] as Form1;
            
            // التحقق من وجود النافذة الرئيسية
            if (mainForm != null)
            {
                // إظهار النافذة الرئيسية مرة أخرى
                mainForm.Show();
                
                // جعل النافذة الرئيسية في المقدمة ونشطة
                mainForm.BringToFront();
                mainForm.Activate();
            }
            else
            {
                // في حالة عدم وجود النافذة الرئيسية، إنشاء نسخة جديدة
                Form1 newMainForm = new Form1();
                newMainForm.Show();
            }
            
            // إغلاق النافذة الحالية (Form2)
            // استخدام Close() لإغلاق النافذة نهائياً وتحرير الذاكرة
            this.Close();
        }
        catch (Exception ex)
        {
            // في حالة حدوث خطأ، إظهار رسالة خطأ
            MessageBox.Show($"حدث خطأ أثناء العودة إلى غرفة القيادة: {ex.Message}", 
                          "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// حدث تحميل النافذة
    /// يتم استدعاؤه عند فتح النافذة لأول مرة
    /// </summary>
    /// <param name="sender">المكون الذي أثار الحدث</param>
    /// <param name="e">معلومات الحدث</param>
    private void Form2_Load(object sender, EventArgs e)
    {
        // يمكن إضافة أي كود تهيئة إضافي هنا
        // مثل تحميل بيانات الغرفة الفرعية أو إعداد المراقبة
    }

    /// <summary>
    /// حدث إغلاق النافذة
    /// يتم استدعاؤه عند محاولة إغلاق النافذة
    /// </summary>
    /// <param name="sender">المكون الذي أثار الحدث</param>
    /// <param name="e">معلومات الحدث</param>
    private void Form2_FormClosing(object sender, FormClosingEventArgs e)
    {
        // التأكد من إظهار النافذة الرئيسية عند إغلاق هذه النافذة
        Form1? mainForm = Application.OpenForms["Form1"] as Form1;
        if (mainForm != null && !mainForm.Visible)
        {
            mainForm.Show();
        }
    }
}
