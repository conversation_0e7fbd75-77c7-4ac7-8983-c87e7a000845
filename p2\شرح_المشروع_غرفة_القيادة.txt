===============================================
تمرين 2: العودة إلى غرفة القيادة - Hide and Return
===============================================

📋 وصف المشروع:
--------------
هذا التمرين يهدف إلى تعليم الطلاب كيفية إدارة النوافذ المتقدمة في Windows Forms
باستخدام Hide() و Show() مع Application.OpenForms للتنقل الذكي بين النوافذ.

🎯 أهداف التعلم:
-----------------
1. فهم الفرق بين Hide() و Close()
2. تعلم كيفية إخفاء النوافذ مؤقتاً
3. استخدام Application.OpenForms للوصول للنوافذ المفتوحة
4. إدارة دورة حياة النوافذ بطريقة احترافية
5. تطبيق نمط التنقل المتقدم بين النوافذ
6. فهم مفهوم النوافذ المخفية vs المغلقة

🔧 المتطلبات التقنية:
--------------------
- .NET Framework 4.7.2 أو أحدث
- Visual Studio 2019 أو أحدث
- Windows Forms Application
- نظام التشغيل Windows

📁 هيكل المشروع:
-----------------
FormNavigationApp/
├── Program.cs              (نقطة البداية للتطبيق)
├── Form1.cs               (غرفة القيادة الرئيسية)
├── Form1.Designer.cs      (تصميم غرفة القيادة)
├── Form2.cs               (الغرفة الفرعية)
├── Form2.Designer.cs      (تصميم الغرفة الفرعية)
└── FormNavigationApp.csproj (ملف إعدادات المشروع)

🎨 وصف واجهة المستخدم:
-----------------------

📱 Form1 (غرفة القيادة الرئيسية):
- العنوان: "غرفة القيادة الرئيسية"
- يحتوي على زر "ادخل غرفة التحكم الفرعية"
- لون الخلفية: أزرق داكن (DarkBlue)
- أيقونة قيادة أو نص ترحيبي
- حجم النافذة: 500x400

📱 Form2 (الغرفة الفرعية):
- العنوان: "غرفة التحكم الفرعية"
- يحتوي على زر "رجوع إلى غرفة القيادة"
- لون الخلفية: أخضر داكن (DarkGreen)
- نص توضيحي للغرفة الفرعية
- حجم النافذة: 450x350

⚙️ وظائف التطبيق:
------------------
1. عند تشغيل التطبيق، يظهر Form1 (غرفة القيادة)
2. عند الضغط على "ادخل غرفة التحكم الفرعية":
   - يتم إنشاء Form2
   - يتم إخفاء Form1 باستخدام this.Hide()
   - يتم عرض Form2 باستخدام Show()
3. عند الضغط على "رجوع" في Form2:
   - يتم البحث عن Form1 في Application.OpenForms
   - يتم إظهار Form1 باستخدام Show()
   - يتم إغلاق Form2 باستخدام this.Close()

🔍 المفاهيم المستخدمة:
----------------------
- Windows Forms Navigation
- Hide() vs Close() vs Show()
- Application.OpenForms Collection
- Form Lifecycle Management
- Event Handling المتقدم
- Memory Management للنوافذ
- Form State Management

📚 الفرق بين Hide() و Close():
-------------------------------

🔹 Hide():
- يخفي النافذة مؤقتاً من الشاشة
- النافذة تبقى في الذاكرة
- يمكن إظهارها مرة أخرى باستخدام Show()
- جميع البيانات والحالة محفوظة
- النافذة ما زالت موجودة في Application.OpenForms

🔹 Close():
- يغلق النافذة نهائياً
- يتم تحرير الذاكرة المستخدمة
- لا يمكن إظهارها مرة أخرى
- جميع البيانات تضيع
- النافذة تُحذف من Application.OpenForms

🔹 Show():
- يعرض النافذة على الشاشة
- يمكن استخدامه مع النوافذ المخفية أو الجديدة
- النافذة تصبح مرئية ونشطة

📊 Application.OpenForms:
-------------------------
- مجموعة تحتوي على جميع النوافذ المفتوحة
- يمكن الوصول للنوافذ بالاسم أو الفهرس
- مفيد للتنقل بين النوافذ الموجودة
- يتم تحديثه تلقائياً عند فتح/إغلاق النوافذ

مثال الاستخدام:
Form1 mainForm = Application.OpenForms["Form1"] as Form1;
if (mainForm != null)
{
    mainForm.Show();
}

🚀 خطوات تشغيل المشروع:
------------------------
1. افتح Visual Studio
2. افتح المشروع FormNavigationApp.sln
3. اضغط F5 أو "Start Debugging"
4. ستظهر غرفة القيادة الرئيسية
5. اضغط "ادخل غرفة التحكم الفرعية"
6. ستختفي النافذة الأولى وتظهر الثانية
7. اضغط "رجوع" للعودة للنافذة الأولى

🎓 ما ستتعلمه من هذا التمرين:
------------------------------
1. إدارة النوافذ المتقدمة في Windows Forms
2. استخدام Hide() لإخفاء النوافذ مؤقتاً
3. الوصول للنوافذ المفتوحة باستخدام Application.OpenForms
4. تطبيق نمط التنقل الاحترافي
5. إدارة ذاكرة النوافذ بكفاءة
6. فهم دورة حياة النوافذ

💡 نصائح مهمة:
---------------
- استخدم Hide() عندما تريد العودة للنافذة لاحقاً
- استخدم Close() عندما لا تحتاج النافذة مرة أخرى
- تحقق دائماً من وجود النافذة في OpenForms قبل الوصول إليها
- احرص على إدارة الذاكرة بشكل صحيح

🔧 تطويرات مقترحة:
-------------------
1. إضافة المزيد من الغرف (Form3, Form4)
2. إنشاء نظام تنقل متقدم مع تاريخ
3. إضافة انتقالات مرئية بين النوافذ
4. حفظ حالة النوافذ عند الإخفاء
5. إضافة نظام أذونات للوصول للغرف

🎯 سيناريو الاستخدام:
---------------------
تخيل أن لديك نظام إدارة مبنى:
- غرفة القيادة الرئيسية: مركز التحكم الأساسي
- الغرف الفرعية: أقسام مختلفة (الأمان، التكييف، الإضاءة)
- المستخدم ينتقل بين الغرف حسب الحاجة
- يمكن العودة للمركز الرئيسي في أي وقت

🔒 أمان النوافذ:
-----------------
- تحقق من صحة النوافذ قبل الوصول إليها
- تعامل مع الحالات الاستثنائية
- تأكد من عدم فتح نوافذ مكررة
- أغلق النوافذ غير المستخدمة لتوفير الذاكرة

===============================================
هذا التمرين يعتبر خطوة متقدمة في تعلم Windows Forms
ويؤهلك لبناء تطبيقات احترافية متعددة النوافذ
===============================================
