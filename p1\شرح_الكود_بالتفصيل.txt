===============================================
شرح كود الآلة الحاسبة بالتفصيل - C#
===============================================

📋 نظرة عامة على الكود:
-----------------------
هذا الكود يحتوي على برنامج آلة حاسبة كامل مكتوب بلغة C# يستخدم مفاهيم البرمجة الأساسية.

🔧 تحليل الكود سطر بسطر:
-------------------------

1️⃣ استيراد المكتبات:
using System;
- يستورد مكتبة System التي تحتوي على الفئات الأساسية مثل Console

2️⃣ تعريف الـ Namespace والفئة:
namespace Calculator
{
    class Program
- ينشئ مساحة اسم تسمى Calculator
- ينشئ فئة تسمى Program

3️⃣ الدالة الرئيسية Main:
static void Main(string[] args)
- نقطة البداية لتنفيذ البرنامج
- static: تعني أن الدالة تنتمي للفئة وليس لكائن معين
- void: تعني أن الدالة لا ترجع قيمة
- string[] args: معاملات سطر الأوامر

4️⃣ إعداد الترميز:
Console.OutputEncoding = System.Text.Encoding.UTF8;
- يضبط ترميز الإخراج لدعم النصوص العربية

5️⃣ الحلقة الرئيسية:
bool continueCalculating = true;
while (continueCalculating)
- متغير boolean للتحكم في استمرار البرنامج
- حلقة while تستمر حتى يختار المستخدم الخروج

6️⃣ معالجة الأخطاء:
try
{
    // الكود الرئيسي
}
catch (Exception ex)
{
    // معالجة الأخطاء
}
- try-catch لالتقاط ومعالجة الأخطاء

===============================================
شرح الدوال (Methods):
===============================================

🔹 DisplayMenu():
-----------------
static void DisplayMenu()
- دالة لعرض قائمة العمليات المتاحة
- static: تنتمي للفئة
- void: لا ترجع قيمة
- تستخدم Console.WriteLine لطباعة النصوص

🔹 GetUserChoice():
-------------------
static int GetUserChoice()
- تقرأ اختيار المستخدم من لوحة المفاتيح
- ترجع int (رقم صحيح)
- تستخدم int.TryParse للتحقق من صحة الإدخال
- ترمي استثناء إذا كان الإدخال غير صحيح

🔹 GetNumber():
---------------
static double GetNumber(string prompt)
- تقرأ رقم من المستخدم
- تأخذ معامل string للنص المطلوب عرضه
- ترجع double (رقم عشري)
- تستخدم double.TryParse للتحقق من صحة الإدخال

🔹 PerformCalculation():
------------------------
static double PerformCalculation(int operation, double num1, double num2)
- تنفذ العملية الحسابية المطلوبة
- تأخذ ثلاثة معاملات: نوع العملية والرقمين
- تستخدم switch statement للاختيار بين العمليات
- تتعامل مع حالة القسمة على صفر

🔹 DisplayResult():
-------------------
static void DisplayResult(int operation, double num1, double num2, double result)
- تعرض نتيجة العملية بشكل منسق
- تستدعي GetOperationSymbol للحصول على رمز العملية
- تستخدم F2: لعرض رقمين بعد الفاصلة العشرية

🔹 GetOperationSymbol():
------------------------
static string GetOperationSymbol(int operation)
- ترجع رمز العملية الحسابية
- تستخدم switch expression (ميزة حديثة في C#)

🔹 AskToContinue():
-------------------
static bool AskToContinue()
- تسأل المستخدم إذا كان يريد المتابعة
- ترجع true أو false
- تستخدم ToLower() و Trim() لمعالجة النص

===============================================
المفاهيم المستخدمة في الكود:
===============================================

🔸 Variables (المتغيرات):
- bool continueCalculating
- int choice
- double num1, num2, result
- string input, response

🔸 Data Types (أنواع البيانات):
- int: للأرقام الصحيحة
- double: للأرقام العشرية
- string: للنصوص
- bool: للقيم المنطقية

🔸 Control Structures (هياكل التحكم):
- while loop: للتكرار
- if-else: للشروط
- switch: للاختيار بين عدة خيارات
- try-catch: لمعالجة الأخطاء

🔸 Methods (الدوال):
- static methods: دوال تنتمي للفئة
- parameters: معاملات الدوال
- return types: أنواع القيم المرجعة

🔸 Exception Handling (معالجة الأخطاء):
- ArgumentException: للمعاملات غير الصحيحة
- DivideByZeroException: للقسمة على صفر
- try-catch blocks: لالتقاط الأخطاء

===============================================
نصائح للمبتدئين:
===============================================

✅ فهم التدفق:
1. البرنامج يبدأ من Main
2. يعرض القائمة
3. يقرأ اختيار المستخدم
4. يقرأ الأرقام
5. ينفذ العملية
6. يعرض النتيجة
7. يسأل عن المتابعة

✅ أهمية التحقق من الإدخال:
- استخدام TryParse بدلاً من Parse
- التعامل مع الحالات الاستثنائية
- عرض رسائل خطأ واضحة

✅ تنظيم الكود:
- تقسيم الكود إلى دوال منفصلة
- استخدام أسماء واضحة للدوال والمتغيرات
- إضافة تعليقات توضيحية

===============================================
تمارين للتطوير:
===============================================

🎯 تمارين سهلة:
1. إضافة عمليات جديدة (الأس، الجذر التربيعي)
2. تحسين واجهة المستخدم
3. إضافة ألوان للنصوص

🎯 تمارين متوسطة:
1. حفظ تاريخ العمليات
2. إضافة عمليات على أكثر من رقمين
3. إنشاء آلة حاسبة علمية

🎯 تمارين متقدمة:
1. إنشاء واجهة رسومية
2. حفظ البيانات في ملف
3. إضافة وحدة تحويل العملات

===============================================
