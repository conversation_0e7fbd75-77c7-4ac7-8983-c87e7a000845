﻿using System;

namespace Calculator
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("===============================================");
            Console.WriteLine("🔢 مرحباً بك في الآلة الحاسبة البسيطة 🔢");
            Console.WriteLine("===============================================");

            bool continueCalculating = true;

            while (continueCalculating)
            {
                try
                {
                    // عرض القائمة الرئيسية
                    DisplayMenu();

                    // قراءة اختيار المستخدم
                    int choice = GetUserChoice();

                    if (choice == 5)
                    {
                        Console.WriteLine("شكراً لاستخدام الآلة الحاسبة! وداعاً 👋");
                        continueCalculating = false;
                        continue;
                    }

                    // قراءة الأرقام من المستخدم
                    double num1 = GetNumber("الرقم الأول");
                    double num2 = GetNumber("الرقم الثاني");

                    // تنفيذ العملية وعرض النتيجة
                    double result = PerformCalculation(choice, num1, num2);
                    DisplayResult(choice, num1, num2, result);

                    // سؤال المستخدم إذا كان يريد المتابعة
                    continueCalculating = AskToContinue();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ حدث خطأ: {ex.Message}");
                    Console.WriteLine("يرجى المحاولة مرة أخرى...\n");
                }
            }
        }

        /// <summary>
        /// عرض قائمة العمليات المتاحة
        /// </summary>
        static void DisplayMenu()
        {
            Console.WriteLine("\n📋 اختر العملية التي تريد تنفيذها:");
            Console.WriteLine("1️⃣  الجمع (+)");
            Console.WriteLine("2️⃣  الطرح (-)");
            Console.WriteLine("3️⃣  الضرب (×)");
            Console.WriteLine("4️⃣  القسمة (÷)");
            Console.WriteLine("5️⃣  الخروج");
            Console.WriteLine("===============================================");
        }

        /// <summary>
        /// قراءة اختيار المستخدم من القائمة
        /// </summary>
        /// <returns>رقم الاختيار</returns>
        static int GetUserChoice()
        {
            Console.Write("👆 اختر رقم العملية (1-5): ");
            string input = Console.ReadLine();

            if (int.TryParse(input, out int choice) && choice >= 1 && choice <= 5)
            {
                return choice;
            }
            else
            {
                throw new ArgumentException("يرجى إدخال رقم صحيح بين 1 و 5");
            }
        }

        /// <summary>
        /// قراءة رقم من المستخدم
        /// </summary>
        /// <param name="prompt">النص المطلوب عرضه للمستخدم</param>
        /// <returns>الرقم المدخل</returns>
        static double GetNumber(string prompt)
        {
            Console.Write($"🔢 أدخل {prompt}: ");
            string input = Console.ReadLine();

            if (double.TryParse(input, out double number))
            {
                return number;
            }
            else
            {
                throw new ArgumentException($"يرجى إدخال رقم صحيح لـ {prompt}");
            }
        }

        /// <summary>
        /// تنفيذ العملية الحسابية المطلوبة
        /// </summary>
        /// <param name="operation">نوع العملية</param>
        /// <param name="num1">الرقم الأول</param>
        /// <param name="num2">الرقم الثاني</param>
        /// <returns>نتيجة العملية</returns>
        static double PerformCalculation(int operation, double num1, double num2)
        {
            switch (operation)
            {
                case 1: // الجمع
                    return num1 + num2;

                case 2: // الطرح
                    return num1 - num2;

                case 3: // الضرب
                    return num1 * num2;

                case 4: // القسمة
                    if (num2 == 0)
                    {
                        throw new DivideByZeroException("لا يمكن القسمة على صفر!");
                    }
                    return num1 / num2;

                default:
                    throw new ArgumentException("عملية غير صحيحة");
            }
        }

        /// <summary>
        /// عرض نتيجة العملية الحسابية
        /// </summary>
        /// <param name="operation">نوع العملية</param>
        /// <param name="num1">الرقم الأول</param>
        /// <param name="num2">الرقم الثاني</param>
        /// <param name="result">النتيجة</param>
        static void DisplayResult(int operation, double num1, double num2, double result)
        {
            string operationSymbol = GetOperationSymbol(operation);

            Console.WriteLine("\n✅ النتيجة:");
            Console.WriteLine("===============================================");
            Console.WriteLine($"🔢 {num1} {operationSymbol} {num2} = {result:F2}");
            Console.WriteLine("===============================================");
        }

        /// <summary>
        /// الحصول على رمز العملية
        /// </summary>
        /// <param name="operation">نوع العملية</param>
        /// <returns>رمز العملية</returns>
        static string GetOperationSymbol(int operation)
        {
            return operation switch
            {
                1 => "+",
                2 => "-",
                3 => "×",
                4 => "÷",
                _ => "?"
            };
        }

        /// <summary>
        /// سؤال المستخدم إذا كان يريد المتابعة
        /// </summary>
        /// <returns>true إذا كان يريد المتابعة، false إذا كان يريد الخروج</returns>
        static bool AskToContinue()
        {
            Console.WriteLine("\n🤔 هل تريد تنفيذ عملية أخرى؟");
            Console.Write("اكتب 'نعم' أو 'y' للمتابعة، أي شيء آخر للخروج: ");
            string response = Console.ReadLine()?.ToLower().Trim();

            return response == "نعم" || response == "y" || response == "yes";
        }
    }
}
