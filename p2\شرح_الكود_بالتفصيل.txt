===============================================
شرح كود مشروع غرفة القيادة بالتفصيل
===============================================

📋 نظرة عامة على ملفات المشروع:
-------------------------------

1️⃣ Program.cs - نقطة البداية
2️⃣ Form1.cs - غرفة القيادة الرئيسية
3️⃣ Form1.Designer.cs - تصميم غرفة القيادة
4️⃣ Form2.cs - غرفة التحكم الفرعية
5️⃣ Form2.Designer.cs - تصميم غرفة التحكم الفرعية

===============================================
📁 ملف Form1.cs (غرفة القيادة الرئيسية):
===============================================

namespace FormNavigationApp;

public partial class Form1 : Form
{
    public Form1()
    {
        InitializeComponent();
    }

    private void btnEnterSubRoom_Click(object sender, EventArgs e)
    {
        try
        {
            Form2 subControlRoom = new Form2();
            this.Hide();
            subControlRoom.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", 
                          MessageBoxButtons.OK, MessageBoxIcon.Error);
            this.Show();
        }
    }
}

🔍 شرح Form1.cs سطر بسطر:

1. namespace FormNavigationApp;
   - يحدد مساحة الاسم للمشروع

2. public partial class Form1 : Form
   - partial: الفئة مقسمة على عدة ملفات
   - : Form ترث من الفئة الأساسية Form

3. public Form1()
   - المنشئ (Constructor) للفئة Form1

4. InitializeComponent();
   - يهيئ جميع المكونات المرئية من ملف Designer

5. private void btnEnterSubRoom_Click(object sender, EventArgs e)
   - معالج حدث الضغط على زر "ادخل غرفة التحكم الفرعية"
   - object sender: المكون الذي أثار الحدث
   - EventArgs e: معلومات إضافية عن الحدث

6. Form2 subControlRoom = new Form2();
   - إنشاء نسخة جديدة من Form2 (الغرفة الفرعية)

7. this.Hide();
   - إخفاء النافذة الحالية (Form1) مؤقتاً
   - this: يشير إلى النافذة الحالية
   - Hide(): يخفي النافذة دون إغلاقها

8. subControlRoom.Show();
   - عرض النافذة الجديدة (Form2)
   - Show(): يعرض النافذة بشكل غير مودال

9. try-catch block:
   - try: يحاول تنفيذ الكود
   - catch: يلتقط الأخطاء ويعالجها
   - Exception ex: متغير يحتوي على معلومات الخطأ

===============================================
📁 ملف Form2.cs (غرفة التحكم الفرعية):
===============================================

private void btnReturnToMain_Click(object sender, EventArgs e)
{
    try
    {
        Form1? mainForm = Application.OpenForms["Form1"] as Form1;
        
        if (mainForm != null)
        {
            mainForm.Show();
            mainForm.BringToFront();
            mainForm.Activate();
        }
        else
        {
            Form1 newMainForm = new Form1();
            newMainForm.Show();
        }
        
        this.Close();
    }
    catch (Exception ex)
    {
        MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", 
                      MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}

🔍 شرح Form2.cs سطر بسطر:

1. Form1? mainForm = Application.OpenForms["Form1"] as Form1;
   - Application.OpenForms: مجموعة تحتوي على جميع النوافذ المفتوحة
   - ["Form1"]: البحث عن نافذة باسم "Form1"
   - as Form1: تحويل النتيجة إلى نوع Form1
   - ?: يعني أن المتغير يمكن أن يكون null

2. if (mainForm != null)
   - التحقق من وجود النافذة الرئيسية
   - != null: ليس فارغ

3. mainForm.Show();
   - إظهار النافذة الرئيسية المخفية

4. mainForm.BringToFront();
   - جعل النافذة في المقدمة

5. mainForm.Activate();
   - تفعيل النافذة (جعلها النافذة النشطة)

6. else
   - في حالة عدم وجود النافذة الرئيسية

7. Form1 newMainForm = new Form1();
   - إنشاء نسخة جديدة من النافذة الرئيسية

8. this.Close();
   - إغلاق النافذة الحالية نهائياً
   - Close(): يغلق النافذة ويحرر الذاكرة

===============================================
📁 ملف Form1.Designer.cs (تصميم غرفة القيادة):
===============================================

🔍 المكونات الرئيسية:

1. btnEnterSubRoom (الزر الرئيسي):
   - BackColor = Color.White: لون الخلفية أبيض
   - Font = Tahoma, 14F, Bold: خط عريض حجم 14
   - ForeColor = Color.DarkBlue: لون النص أزرق داكن
   - Location = (125, 200): موقع الزر
   - Size = (250, 60): حجم الزر
   - Text = "ادخل غرفة التحكم الفرعية": النص المعروض
   - Click += btnEnterSubRoom_Click: ربط الحدث

2. lblWelcome (العنوان الرئيسي):
   - Font = Tahoma, 18F, Bold: خط كبير وعريض
   - ForeColor = Color.White: لون أبيض
   - Text = "غرفة القيادة الرئيسية"

3. lblTitle (العنوان الفرعي):
   - Font = Tahoma, 12F: خط متوسط
   - ForeColor = Color.LightBlue: لون أزرق فاتح
   - Text = "مركز التحكم الرئيسي"

4. Form1 (النافذة نفسها):
   - BackColor = Color.DarkBlue: خلفية زرقاء داكنة
   - ClientSize = (500, 400): حجم النافذة
   - Text = "غرفة القيادة الرئيسية": عنوان النافذة
   - StartPosition = CenterScreen: تظهر في وسط الشاشة

===============================================
📁 ملف Form2.Designer.cs (تصميم غرفة التحكم الفرعية):
===============================================

🔍 المكونات الرئيسية:

1. btnReturnToMain (زر الرجوع):
   - BackColor = Color.White: خلفية بيضاء
   - ForeColor = Color.DarkGreen: نص أخضر داكن
   - Text = "رجوع إلى غرفة القيادة"
   - Click += btnReturnToMain_Click: ربط الحدث

2. lblWelcome (العنوان الرئيسي):
   - Font = Tahoma, 18F, Bold: خط كبير
   - ForeColor = Color.White: لون أبيض
   - Text = "غرفة التحكم الفرعية"

3. lblStatus (حالة النافذة):
   - Font = Tahoma, 9F, Italic: خط مائل
   - ForeColor = Color.LightGreen: أخضر فاتح
   - Text = "النافذة الرئيسية مخفية ويمكن العودة إليها"

4. Form2 (النافذة):
   - BackColor = Color.DarkGreen: خلفية خضراء داكنة
   - ClientSize = (450, 350): حجم أصغر من Form1
   - Text = "غرفة التحكم الفرعية"

===============================================
🔧 المفاهيم التقنية المستخدمة:
===============================================

1. Hide() vs Show() vs Close():
   - Hide(): يخفي النافذة مؤقتاً (تبقى في الذاكرة)
   - Show(): يعرض النافذة
   - Close(): يغلق النافذة نهائياً (يحرر الذاكرة)

2. Application.OpenForms:
   - مجموعة تحتوي على جميع النوافذ المفتوحة
   - يمكن الوصول للنوافذ بالاسم أو الفهرس
   - تتضمن النوافذ المخفية والمرئية

3. Form Navigation Pattern:
   - نمط التنقل بين النوافذ
   - إخفاء النافذة الحالية عند فتح جديدة
   - العودة للنافذة المخفية عند الحاجة

4. Exception Handling:
   - try-catch لمعالجة الأخطاء
   - MessageBox لعرض رسائل الخطأ
   - التعافي من الأخطاء بإظهار النافذة

5. Form Events:
   - Click Events للأزرار
   - Load Event عند تحميل النافذة
   - FormClosing Event عند إغلاق النافذة

6. Memory Management:
   - استخدام Hide() للحفاظ على البيانات
   - استخدام Close() لتحرير الذاكرة
   - إدارة دورة حياة النوافذ

===============================================
💡 نصائح للمبتدئين:
===============================================

✅ فهم الفرق بين Hide() و Close():
- Hide(): للإخفاء المؤقت
- Close(): للإغلاق النهائي

✅ استخدام Application.OpenForms:
- للوصول للنوافذ الموجودة
- تجنب إنشاء نوافذ مكررة

✅ معالجة الأخطاء:
- استخدام try-catch دائماً
- عرض رسائل خطأ واضحة
- التعافي من الأخطاء بشكل مناسب

✅ تصميم واجهة المستخدم:
- استخدام ألوان متناسقة
- خطوط واضحة ومقروءة
- ترتيب منطقي للمكونات

===============================================
