namespace FormNavigationApp;

/// <summary>
/// Form1 - غرفة القيادة الرئيسية
/// هذه النافذة تمثل مركز التحكم الرئيسي في النظام
/// </summary>
public partial class Form1 : Form
{
    /// <summary>
    /// منشئ Form1 - غرفة القيادة الرئيسية
    /// يقوم بتهيئة المكونات والإعدادات الأساسية
    /// </summary>
    public Form1()
    {
        InitializeComponent();
    }

    /// <summary>
    /// حدث الضغط على زر "ادخل غرفة التحكم الفرعية"
    /// يقوم بإخفاء النافذة الحالية وفتح النافذة الفرعية
    /// </summary>
    /// <param name="sender">المكون الذي أثار الحدث</param>
    /// <param name="e">معلومات الحدث</param>
    private void btnEnterSubRoom_Click(object sender, EventArgs e)
    {
        try
        {
            // إنشاء نسخة جديدة من Form2 (الغرفة الفرعية)
            Form2 subControlRoom = new Form2();

            // إخفاء النافذة الحالية (Form1) مؤقتاً
            // استخدام Hide() بدلاً من Close() للحفاظ على النافذة في الذاكرة
            this.Hide();

            // فتح النافذة الفرعية
            // استخدام Show() لفتح النافذة بشكل غير مودال
            subControlRoom.Show();
        }
        catch (Exception ex)
        {
            // في حالة حدوث خطأ، إظهار رسالة خطأ وإظهار النافذة مرة أخرى
            MessageBox.Show($"حدث خطأ أثناء فتح غرفة التحكم الفرعية: {ex.Message}",
                          "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            this.Show(); // إظهار النافذة مرة أخرى في حالة الخطأ
        }
    }

    /// <summary>
    /// حدث تحميل النافذة
    /// يتم استدعاؤه عند فتح النافذة لأول مرة
    /// </summary>
    /// <param name="sender">المكون الذي أثار الحدث</param>
    /// <param name="e">معلومات الحدث</param>
    private void Form1_Load(object sender, EventArgs e)
    {
        // يمكن إضافة أي كود تهيئة إضافي هنا
        // مثل تحميل إعدادات المستخدم أو التحقق من الأذونات
    }
}
