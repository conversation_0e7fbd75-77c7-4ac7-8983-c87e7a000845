===============================================
شرح مشروع الآلة الحاسبة - C# Console Application
===============================================

📋 وصف المشروع:
--------------
هذا المشروع عبارة عن آلة حاسبة بسيطة مكتوبة بلغة C# تعمل في وضع Console Application.
تقوم الآلة الحاسبة بتنفيذ العمليات الحسابية الأساسية الأربع: الجمع، الطرح، الضرب، والقسمة.

🎯 أهداف المشروع:
-----------------
1. تعلم أساسيات البرمجة بلغة C#
2. فهم كيفية التعامل مع المدخلات والمخرجات في Console
3. تطبيق المفاهيم الأساسية مثل المتغيرات والدوال والشروط
4. التعامل مع الاستثناءات (Exception Handling)
5. إنشاء واجهة مستخدم بسيطة وسهلة الاستخدام

🔧 المتطلبات التقنية:
--------------------
- .NET Framework أو .NET Core
- Visual Studio أو أي محرر نصوص يدعم C#
- نظام التشغيل Windows, macOS, أو Linux

📁 هيكل المشروع:
-----------------
Calculator/
├── Program.cs          (الملف الرئيسي للبرنامج)
├── Calculator.csproj   (ملف إعدادات المشروع)
└── bin/               (مجلد الملفات التنفيذية)

⚙️ وظائف البرنامج:
------------------
1. عرض قائمة العمليات المتاحة
2. قراءة اختيار المستخدم للعملية
3. قراءة الرقمين من المستخدم
4. تنفيذ العملية الحسابية المطلوبة
5. عرض النتيجة
6. إمكانية تكرار العمليات أو الخروج من البرنامج

🔍 المفاهيم المستخدمة:
----------------------
- Variables (المتغيرات)
- Data Types (أنواع البيانات)
- Methods (الدوال)
- Conditional Statements (الجمل الشرطية)
- Loops (الحلقات)
- Exception Handling (معالجة الأخطاء)
- User Input/Output (المدخلات والمخرجات)

🚀 كيفية تشغيل المشروع:
-----------------------
1. افتح Command Prompt أو Terminal
2. انتقل إلى مجلد المشروع
3. اكتب الأمر: dotnet run
4. اتبع التعليمات التي تظهر على الشاشة

📝 ملاحظات مهمة:
-----------------
- البرنامج يتعامل مع الأرقام العشرية (double)
- يتم التحقق من صحة المدخلات
- يتم التعامل مع حالة القسمة على صفر
- واجهة المستخدم باللغة العربية

🎓 ما ستتعلمه من هذا المشروع:
------------------------------
1. كيفية إنشاء مشروع C# جديد
2. استخدام Console.WriteLine و Console.ReadLine
3. تحويل النصوص إلى أرقام
4. استخدام switch statement
5. إنشاء دوال مخصصة
6. معالجة الأخطاء باستخدام try-catch
7. استخدام الحلقات للتكرار

===============================================
تم إنشاء هذا المشروع لأغراض تعليمية
===============================================
